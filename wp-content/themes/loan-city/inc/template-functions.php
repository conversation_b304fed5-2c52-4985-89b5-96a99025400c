<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package Loan_City
 */

/**
 * Add custom classes to the body element.
 *
 * @param array $classes Classes for the body element.
 * @return array
 */
function loan_city_body_classes( $classes ) {
    // Add a class if there is a custom header.
    if ( has_header_image() ) {
        $classes[] = 'has-header-image';
    }

    // Add a class if there is a custom background.
    if ( get_background_image() || get_background_color() !== 'ffffff' ) {
        $classes[] = 'has-custom-background';
    }

    return $classes;
}
add_filter( 'body_class', 'loan_city_body_classes' );

/**
 * Add a pingback url auto-discovery header for single posts, pages, or attachments.
 */
function loan_city_pingback_header() {
    if ( is_singular() && pings_open() ) {
        printf( '<link rel="pingback" href="%s">', esc_url( get_bloginfo( 'pingback_url' ) ) );
    }
}
add_action( 'wp_head', 'loan_city_pingback_header' );

/**
 * Adds custom classes to the array of post classes.
 *
 * @param array $classes Classes for the post element.
 * @return array
 */
function loan_city_post_classes( $classes ) {
    if ( is_singular() ) {
        $classes[] = 'singular-post';
    } else {
        $classes[] = 'archive-post';
    }

    return $classes;
}
add_filter( 'post_class', 'loan_city_post_classes' );

/**
 * Filter the excerpt length.
 *
 * @param int $length Excerpt length.
 * @return int Modified excerpt length.
 */
function loan_city_excerpt_length( $length ) {
    return 20;
}
add_filter( 'excerpt_length', 'loan_city_excerpt_length' );

/**
 * Filter the excerpt "read more" string.
 *
 * @param string $more "Read more" excerpt string.
 * @return string Modified "read more" excerpt string.
 */
function loan_city_excerpt_more( $more ) {
    return '&hellip; <a href="' . esc_url( get_permalink() ) . '" class="more-link">' . esc_html__( 'Read More', 'loan-city' ) . ' <span class="screen-reader-text">' . get_the_title() . '</span></a>';
}
add_filter( 'excerpt_more', 'loan_city_excerpt_more' );

/**
 * Add a custom image size for featured images.
 */
function loan_city_custom_image_sizes() {
    add_image_size( 'loan-city-featured', 1200, 600, true );
    add_image_size( 'loan-city-thumbnail', 600, 400, true );
}
add_action( 'after_setup_theme', 'loan_city_custom_image_sizes' );

/**
 * Add custom image sizes to the media library.
 *
 * @param array $sizes Image sizes.
 * @return array Modified image sizes.
 */
function loan_city_custom_image_sizes_names( $sizes ) {
    return array_merge( $sizes, array(
        'loan-city-featured' => __( 'Featured Image', 'loan-city' ),
        'loan-city-thumbnail' => __( 'Post Thumbnail', 'loan-city' ),
    ) );
}
add_filter( 'image_size_names_choose', 'loan_city_custom_image_sizes_names' );

/**
 * Create a shortcode to display latest articles.
 *
 * @param array $atts Shortcode attributes.
 * @return string HTML output of the latest articles.
 */
function loan_city_latest_articles_shortcode( $atts ) {
    $atts = shortcode_atts(
        array(
            'count'     => 6, // Show 6 by default
            'orderby'   => 'date',
            'order'     => 'DESC',
            'exclude'   => '', // Comma-separated list of post IDs to exclude
            'title'     => 'Latest articles', // Title to display above the articles
            'exclude_current' => 'no', // Whether to exclude the current post
        ),
        $atts,
        'latest_articles'
    );

    // Query articles
    $args = array(
        'post_type'      => 'article',
        'posts_per_page' => intval( $atts['count'] ),
        'orderby'        => $atts['orderby'],
        'order'          => $atts['order'],
        'post_status'    => 'publish',
    );

    // If exclude parameter is provided, add it to the query
    if ( ! empty( $atts['exclude'] ) ) {
        $args['post__not_in'] = explode( ',', $atts['exclude'] );
    }

    // If exclude_current is set to yes and we're on a single post, exclude the current post
    if ( $atts['exclude_current'] === 'yes' || $atts['exclude'] === 'current' ) {
        global $post;
        if ( isset( $post->ID ) ) {
            if ( isset( $args['post__not_in'] ) ) {
                $args['post__not_in'][] = $post->ID;
            } else {
                $args['post__not_in'] = array( $post->ID );
            }
        }
    }

    $articles_query = new WP_Query( $args );

    if ( ! $articles_query->have_posts() ) {
        return '<p>' . esc_html__( 'No articles found.', 'loan-city' ) . '</p>';
    }

    // Start output buffering
    ob_start();
    ?>

    <div class="latest-articles-section">
        <h2><?php echo esc_html( $atts['title'] ); ?></h2>

        <div class="latest-articles-grid">
            <?php while ( $articles_query->have_posts() ) : $articles_query->the_post(); ?>
                <div class="latest-article-item">
                    <div class="latest-article-image">
                        <a href="<?php the_permalink(); ?>">
                            <?php if ( has_post_thumbnail() ) : ?>
                                <?php the_post_thumbnail( 'loan-city-thumbnail', array( 'alt' => get_the_title() ) ); ?>
                            <?php else : ?>
                                <img src="<?php echo esc_url( get_template_directory_uri() . '/assets/images/placeholder-image.jpg' ); ?>" alt="<?php the_title_attribute(); ?>" />
                            <?php endif; ?>
                        </a>
                    </div>

                    <h3 class="latest-article-title">
                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                    </h3>

                    <div class="latest-article-excerpt">
                        <?php the_excerpt(); ?>
                    </div>

                    <div class="latest-article-date">
                        <?php echo get_the_date(); ?>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>
    </div>

    <?php
    // Reset post data
    wp_reset_postdata();

    // Get the buffered content
    $output = ob_get_clean();

    return $output;
}
add_shortcode( 'latest_articles', 'loan_city_latest_articles_shortcode' );

/**
 * Extract the featured article ID from the current page content.
 *
 * @return int The featured article ID or 0 if not found.
 */
function loan_city_get_featured_article_id() {
    global $post;
    $article_id = 0;

    if ($post && $post->post_content) {
        // First try to find the featured article block with its attributes
        if (preg_match('/<!-- wp:loan-city\/featured-article\s+(\{.*?\})\s+\/-->/', $post->post_content, $matches)) {
            if (!empty($matches[1])) {
                $block_data = json_decode($matches[1], true);
                if (isset($block_data['articleId']) && intval($block_data['articleId']) > 0) {
                    $article_id = intval($block_data['articleId']);
                }
            }
        }

        // If not found, try to find it in a nested block structure
        if ($article_id === 0) {
            if (preg_match('/<!-- wp:loan-city\/featured-article\s+(\{.*?\})\s+-->.*?<!-- \/wp:loan-city\/featured-article -->/s', $post->post_content, $matches)) {
                if (!empty($matches[1])) {
                    $block_data = json_decode($matches[1], true);
                    if (isset($block_data['articleId']) && intval($block_data['articleId']) > 0) {
                        $article_id = intval($block_data['articleId']);
                    }
                }
            }
        }
    }

    return $article_id;
}

/**
 * Create a shortcode to display latest articles excluding the featured article.
 *
 * @param array $atts Shortcode attributes.
 * @return string HTML output of the latest articles.
 */
function loan_city_latest_articles_exclude_featured_shortcode( $atts ) {
    $featured_article_id = loan_city_get_featured_article_id();

    // Debug: Check if we're getting the featured article ID correctly
    $debug_enabled = false; // Set to true for debugging
    if ($debug_enabled) {
        error_log("Latest articles exclude featured - Featured article ID: " . $featured_article_id);
        global $post;
        if ($post) {
            error_log("Global post ID: " . $post->ID . ", Title: " . $post->post_title);
            error_log("Global post content preview: " . substr($post->post_content, 0, 200));
        } else {
            error_log("No global post available");
        }
    }

    // If we can't get the featured article ID from global post, try to get it from the blog page directly
    if ($featured_article_id === 0) {
        $blog_page = get_page_by_path('blog');
        if ($blog_page && $blog_page->post_content) {
            if (preg_match('/<!-- wp:loan-city\/featured-article\s+(\{.*?\})\s+\/-->/', $blog_page->post_content, $matches)) {
                if (!empty($matches[1])) {
                    $block_data = json_decode($matches[1], true);
                    if (isset($block_data['articleId']) && intval($block_data['articleId']) > 0) {
                        $featured_article_id = intval($block_data['articleId']);
                        if ($debug_enabled) {
                            error_log("Found featured article ID from blog page: " . $featured_article_id);
                        }
                    }
                }
            }
        }
    }

    // Merge with existing exclude parameter if it exists
    if (!empty($atts['exclude'])) {
        $exclude_ids = explode(',', $atts['exclude']);
        if ($featured_article_id > 0 && !in_array($featured_article_id, $exclude_ids)) {
            $exclude_ids[] = $featured_article_id;
        }
        $atts['exclude'] = implode(',', $exclude_ids);
    } else if ($featured_article_id > 0) {
        $atts['exclude'] = $featured_article_id;
    }

    if ($debug_enabled) {
        error_log("Final exclude list: " . (isset($atts['exclude']) ? $atts['exclude'] : 'none'));
    }

    // Call the original shortcode function with the modified attributes
    return loan_city_latest_articles_shortcode( $atts );
}
add_shortcode( 'latest_articles_exclude_featured', 'loan_city_latest_articles_exclude_featured_shortcode' );

/**
 * Shortcode to verify exclusion functionality - for testing purposes
 * Usage: [verify_exclusion]
 */
function loan_city_verify_exclusion_shortcode() {
    $featured_article_id = loan_city_get_featured_article_id();

    // If we can't get it from global post, try blog page directly
    if ($featured_article_id === 0) {
        $blog_page = get_page_by_path('blog');
        if ($blog_page && $blog_page->post_content) {
            if (preg_match('/<!-- wp:loan-city\/featured-article\s+(\{.*?\})\s+\/-->/', $blog_page->post_content, $matches)) {
                if (!empty($matches[1])) {
                    $block_data = json_decode($matches[1], true);
                    if (isset($block_data['articleId']) && intval($block_data['articleId']) > 0) {
                        $featured_article_id = intval($block_data['articleId']);
                    }
                }
            }
        }
    }

    if ($featured_article_id === 0) {
        return '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-left: 4px solid #ccc;">No featured article ID found</div>';
    }

    // Get the featured article title
    $featured_article = get_post($featured_article_id);
    $featured_title = $featured_article ? $featured_article->post_title : 'Unknown';

    // Get latest articles (same query as the shortcode)
    $args = array(
        'post_type'      => 'article',
        'posts_per_page' => 6,
        'orderby'        => 'date',
        'order'          => 'DESC',
        'post_status'    => 'publish',
        'post__not_in'   => array($featured_article_id),
    );

    $articles_query = new WP_Query($args);
    $latest_article_ids = array();

    if ($articles_query->have_posts()) {
        while ($articles_query->have_posts()) {
            $articles_query->the_post();
            $latest_article_ids[] = get_the_ID();
        }
        wp_reset_postdata();
    }

    $is_excluded = !in_array($featured_article_id, $latest_article_ids);
    $status_color = $is_excluded ? '#4CAF50' : '#f44336';
    $status_text = $is_excluded ? 'WORKING CORRECTLY' : 'NOT WORKING';

    $output = '<div style="background: #f9f9f9; padding: 15px; margin: 20px 0; border-left: 4px solid ' . $status_color . '; font-family: monospace;">';
    $output .= '<h4 style="margin: 0 0 10px 0; color: ' . $status_color . ';">Exclusion Status: ' . $status_text . '</h4>';
    $output .= '<p><strong>Featured Article ID:</strong> ' . $featured_article_id . '</p>';
    $output .= '<p><strong>Featured Article Title:</strong> ' . esc_html($featured_title) . '</p>';
    $output .= '<p><strong>Latest Articles IDs:</strong> ' . implode(', ', $latest_article_ids) . '</p>';
    $output .= '<p><strong>Is Featured Article Excluded:</strong> ' . ($is_excluded ? 'Yes' : 'No') . '</p>';
    $output .= '</div>';

    return $output;
}
add_shortcode( 'verify_exclusion', 'loan_city_verify_exclusion_shortcode' );
